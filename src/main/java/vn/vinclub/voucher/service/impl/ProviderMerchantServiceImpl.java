package vn.vinclub.voucher.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantCreateDto;
import vn.vinclub.voucher.dto.provider.merchant.ProviderMerchantUpdateDto;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.mapper.ProviderMerchantMapper;
import vn.vinclub.voucher.model.BaseEntity;
import vn.vinclub.voucher.model.ProviderMerchant;
import vn.vinclub.voucher.redis.RedisPublish;
import vn.vinclub.voucher.repository.ProviderMerchantRepository;
import vn.vinclub.voucher.service.ProviderMerchantService;
import vn.vinclub.voucher.service.ProviderService;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProviderMerchantServiceImpl implements ProviderMerchantService {
    private final ProviderService providerService;
    private final ProviderMerchantRepository providerMerchantRepository;
    private final RedisPublish redisPublish;

    private final Cache<Long, ProviderMerchant> providerMerchantCacheById = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(100)
            .build();

    private final Cache<String, String> lookupByVClubCodeMap = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(100)
            .build();

    @Override
    @Transactional
    public ProviderMerchant create(ProviderMerchantCreateDto createDto) {
        try (Profiler p = new Profiler(getClass(), "create")) {
            validateAndEnhanceCreateRequest(createDto);
            if (providerMerchantRepository.existsByProviderCodeAndProviderMerchantCodeAndActive(createDto.getProviderCode(), createDto.getProviderMerchantCode(), true)) {
                throw new BusinessLogicException(AppErrorCode.OBJECT_EXISTED, ProviderMerchant.NAME, ProviderMerchant.Fields.providerMerchantCode, createDto.getProviderMerchantCode());
            }
            var providerMerchant = ProviderMerchantMapper.INSTANCE.toEntity(createDto);
            var provider = providerService.getActiveByCode(createDto.getProviderCode());
            providerMerchant.setProviderId(provider.getId());
            return this.providerMerchantRepository.save(providerMerchant);
        }
    }

    @Override
    @Transactional
    public ProviderMerchant update(Long id, ProviderMerchantUpdateDto updateDto) {
        try (Profiler p = new Profiler(getClass(), "update")) {
            var providerMerchant = this.providerMerchantRepository.findByIdAndActive(id, true)
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, ProviderMerchant.NAME, BaseEntity.Fields.id, id));
            applyUpdateRequest(providerMerchant, updateDto);

            var savedProviderMerchant = this.providerMerchantRepository.save(providerMerchant);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PROVIDER_MERCHANT, savedProviderMerchant.getId());
                }
            });
            return savedProviderMerchant;
        }
    }

    @Override
    @Transactional
    public boolean delete(Long id) {
        try (Profiler p = new Profiler(getClass(), "delete")) {
            var providerMerchant = this.providerMerchantRepository.findByIdAndActive(id, true)
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, ProviderMerchant.NAME, BaseEntity.Fields.id, id));

            providerMerchant.setActive(false);
            var deletedProviderMerchant = this.providerMerchantRepository.save(providerMerchant);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_PROVIDER_MERCHANT, deletedProviderMerchant.getId());
                }
            });
            return true;
        }
    }

    @Override
    public ProviderMerchant getById(Long id) {
        try (Profiler p = new Profiler(getClass(), "getById")) {
            var cachedProviderMerchant = providerMerchantCacheById.getIfPresent(id);
            if (cachedProviderMerchant == null) {
                try (Profiler cm = new Profiler(getClass(), "getById - cacheMiss")) {
                    var providerMerchantDb = providerMerchantRepository.findByIdAndActive(id, true)
                            .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, ProviderMerchant.NAME, BaseEntity.Fields.id, id));
                    putToCache(providerMerchantDb);
                    return providerMerchantDb;
                }
            }
            try (Profiler ch = new Profiler(getClass(), "getById - cacheHit")) {
                return cachedProviderMerchant;
            }
        }
    }

    @Override
    public Page<ProviderMerchant> getAllByProviderId(Long providerId, Pageable pageable) {
        try (Profiler p = new Profiler(getClass(), "getAllByProviderId")) {
            var provider = providerService.getActiveById(providerId);
            return getAllByProviderCode(provider.getCode(), pageable);
        }
    }

    @Override
    public Page<ProviderMerchant> getAllByProviderCode(String providerCode, Pageable pageable) {
        try (Profiler p = new Profiler(getClass(), "getAllByProviderCode")) {
            return providerMerchantRepository.findByProviderCodeAndActive(providerCode, true, pageable);
        }
    }

    @Override
    public String lookupByVClubCode(String providerCode, String vclubMerchantCode) {
        try (Profiler p = new Profiler(getClass(), "lookupByVclubCode")) {
            var cachedProviderMerchantCode = lookupByVClubCodeMap.getIfPresent(genLookupCode(providerCode, vclubMerchantCode));
            if (!StringUtils.hasText(cachedProviderMerchantCode)) {
                try (Profiler cm = new Profiler(getClass(), "lookupByVclubCode - cacheMiss")) {
                    var providerMerchant = providerMerchantRepository.findByProviderCodeAndVclubMerchantCodeAndActive(providerCode, vclubMerchantCode, true)
                            .orElse(null);

                    if (providerMerchant == null) {
                        return null;
                    }

                    putToCache(providerMerchant);
                    return providerMerchant.getProviderMerchantCode();
                }
            }
            try (Profiler ch = new Profiler(getClass(), "lookupByVclubCode - cacheHit")) {
                return cachedProviderMerchantCode;
            }
        }
    }

    @Override
    public void invalidateCache(Long id) {
        try (Profiler p = new Profiler(getClass(), "invalidateCache")) {
            var cachedProviderMerchant = providerMerchantCacheById.getIfPresent(id);
            if (Objects.nonNull(cachedProviderMerchant)) {
                lookupByVClubCodeMap.invalidate(genLookupCode(cachedProviderMerchant));
            }
            providerMerchantCacheById.invalidate(id);
        }
    }

    private void putToCache(ProviderMerchant providerMerchant) {
        try (Profiler p = new Profiler(getClass(), "putToCache")) {
            providerMerchantCacheById.put(providerMerchant.getId(), providerMerchant);
            lookupByVClubCodeMap.put(genLookupCode(providerMerchant), providerMerchant.getProviderMerchantCode());
        }
    }

    private String genLookupCode(ProviderMerchant providerMerchant) {
        return String.format("%s:%s", providerMerchant.getProviderCode(), providerMerchant.getVclubMerchantCode());
    }

    private String genLookupCode(String providerCode, String vclubMerchantCode) {
        return String.format("%s:%s", providerCode, vclubMerchantCode);
    }

    private void validateAndEnhanceCreateRequest(ProviderMerchantCreateDto createDto) {
        if (!StringUtils.hasText(createDto.getProviderCode())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderMerchantCreateDto.Fields.providerCode);
        }
        if (!StringUtils.hasText(createDto.getProviderMerchantCode())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderMerchantCreateDto.Fields.providerMerchantCode);
        }
        if (MapUtils.isEmpty(createDto.getDisplayNames())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderMerchantCreateDto.Fields.displayNames);
        }
    }

    private void applyUpdateRequest(ProviderMerchant providerMerchant, ProviderMerchantUpdateDto updateDto) {
        if (MapUtils.isNotEmpty(updateDto.getDisplayNames())) {
            providerMerchant.getDisplayNames().putAll(updateDto.getDisplayNames());
        }

        if (StringUtils.hasText(updateDto.getVclubMerchantCode())) {
            providerMerchant.setVclubMerchantCode(updateDto.getVclubMerchantCode());
        }
    }
}