package vn.vinclub.voucher.service.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import vn.vinclub.common.model.Profiler;
import vn.vinclub.voucher.constant.AppConst;
import vn.vinclub.voucher.constant.AppErrorCode;
import vn.vinclub.voucher.dto.EncryptKeyData;
import vn.vinclub.voucher.dto.provider.ProviderCreateDto;
import vn.vinclub.voucher.dto.provider.ProviderFilterDto;
import vn.vinclub.voucher.dto.provider.ProviderSpecification;
import vn.vinclub.voucher.dto.provider.ProviderUpdateDto;
import vn.vinclub.voucher.enums.EncryptKeyTypeEnum;
import vn.vinclub.voucher.enums.ProviderStatusEnum;
import vn.vinclub.voucher.exception.BusinessLogicException;
import vn.vinclub.voucher.mapper.ProviderMapper;
import vn.vinclub.voucher.model.BaseEntity;
import vn.vinclub.voucher.model.Provider;
import vn.vinclub.voucher.redis.RedisPublish;
import vn.vinclub.voucher.repository.ProviderRepository;
import vn.vinclub.voucher.service.ProviderService;
import vn.vinclub.voucher.util.KeyUtil;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Service
@Slf4j
@RequiredArgsConstructor
public class ProviderServiceImpl extends BaseService implements ProviderService {
    private final ProviderRepository providerRepository;
    private final RedisPublish redisPublish;

    private final Cache<Long, Provider> providerCacheById = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(10)
            .build();
    private final Cache<String, Long> codeToId = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(10)
            .build();

    @PostConstruct
    public void init() {
        reloadCache();
    }

    @Override
    @Transactional
    public Provider create(ProviderCreateDto createDto) {
        try (Profiler p = new Profiler(getClass(), "create")) {
            validateAndEnhanceCreateRequest(createDto);
            // check code is unique
            if (providerRepository.existsByCodeAndActive(createDto.getCode(), true)) {
                throw new BusinessLogicException(AppErrorCode.OBJECT_EXISTED, Provider.NAME, Provider.Fields.code, createDto.getCode());
            }
            var provider = ProviderMapper.INSTANCE.toEntity(createDto);
            return providerRepository.save(provider);
        }
    }

    @Override
    @Transactional
    public Provider update(Long id, ProviderUpdateDto updateDto) {
        try (Profiler p = new Profiler(getClass(), "update")) {
            var provider = providerRepository.findByIdAndActive(id, true)
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, Provider.NAME, BaseEntity.Fields.id, id));
            applyUpdateRequest(provider, updateDto);

            var savedProvider = providerRepository.save(provider);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_VOUCHER_PROVIDER, savedProvider.getId(), savedProvider.getCode());
                }
            });
            return savedProvider;
        }
    }

    @Override
    @Transactional
    public boolean delete(Long id) {
        try (Profiler p = new Profiler(getClass(), "delete")) {
            var provider = providerRepository.findByIdAndActive(id, true)
                    .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, Provider.NAME, BaseEntity.Fields.id, id));

            provider.setActive(false);
            provider.setStatus(ProviderStatusEnum.INACTIVE);
            var deletedProvider = providerRepository.save(provider);

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    redisPublish.sendChange(AppConst.RedisMessage.CHANGE_VOUCHER_PROVIDER, deletedProvider.getId(), deletedProvider.getCode());
                }
            });
            return true;
        }
    }

    @Override
    public Provider getById(Long id) {
        try (Profiler p = new Profiler(getClass(), "getById")) {
            var cachedProvider = providerCacheById.getIfPresent(id);
            if (cachedProvider == null) {
                try (Profiler cm = new Profiler(getClass(), "getById - cacheMiss")) {
                    var providerDb = providerRepository.findById(id)
                            .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, Provider.NAME, BaseEntity.Fields.id, id));
                    putToCache(providerDb);
                    return providerDb;
                }
            }
            try (Profiler ch = new Profiler(getClass(), "getById - cacheHit")) {
                return cachedProvider;
            }
        }
    }

    @Override
    public Provider getByCode(String code) {
        try (Profiler p = new Profiler(getClass(), "getByCode")) {
            Function<String, Provider> cacheMissFn = (String pCode) -> {
                try (Profiler cm = new Profiler(getClass(), "getByCode - cacheMiss")) {
                    var providerDb = providerRepository.findByCodeAndActive(pCode, true)
                            .orElseThrow(() -> new BusinessLogicException(AppErrorCode.NOT_FOUND, Provider.NAME, Provider.Fields.code, pCode));
                    putToCache(providerDb);
                    return providerDb;
                }
            };

            Long id = codeToId.getIfPresent(code);
            if (id == null) {
                return cacheMissFn.apply(code);
            }

            var cachedProvider = providerCacheById.getIfPresent(id);
            if (cachedProvider == null) {
                return cacheMissFn.apply(code);
            }
            try (Profiler ch = new Profiler(getClass(), "getByCode - cacheHit")) {
                return cachedProvider;
            }
        }
    }

    @Override
    public Provider getActiveById(Long id) {
        try (Profiler p = new Profiler(getClass(), "getActiveById")) {
            var provider = getById(id);
            if (Boolean.FALSE.equals(provider.getActive())) {
                throw new BusinessLogicException(AppErrorCode.NOT_FOUND, Provider.NAME, BaseEntity.Fields.id, id);
            }
            if (!ProviderStatusEnum.ACTIVE.equals(provider.getStatus())) {
                throw new BusinessLogicException(AppErrorCode.REQUEST_DATA_INVALID, "Provider status is not active");
            }
            return provider;
        }
    }

    @Override
    public Provider getActiveByCode(String code) {
        try (Profiler p = new Profiler(getClass(), "getActiveByCode")) {
            var provider = getByCode(code);
            if (Boolean.FALSE.equals(provider.getActive())) {
                throw new BusinessLogicException(AppErrorCode.NOT_FOUND, Provider.NAME, Provider.Fields.code, code);
            }
            if (!ProviderStatusEnum.ACTIVE.equals(provider.getStatus())) {
                throw new BusinessLogicException(AppErrorCode.REQUEST_DATA_INVALID, "Provider status is not active");
            }
            return provider;
        }
    }

    @Override
    public Page<Provider> filter(ProviderFilterDto filter, Pageable pageable) {
        try (Profiler p = new Profiler(getClass(), "filter")) {
            return providerRepository.findAll(ProviderSpecification.filterByCriteria(filter), pageable);
        }
    }

    @Override
    public boolean existsByCode(String code) {
        try (Profiler p = new Profiler(getClass(), "existsByCode")) {
            if (StringUtils.hasText(code)) {
                return providerRepository.existsByCodeAndActive(code, true);
            }
            return false;
        }
    }

    @Override
    public PublicKey getPublicKeyByCode(String code) {
        try (Profiler p = new Profiler(getClass(), "getPublicKeyByCode")) {
            var provider = getByCode(code);
            if (Objects.nonNull(provider) && Objects.nonNull(provider.getEncryptionKey()) && Objects.nonNull(provider.getEncryptionKey().getPublicKey())) {
                return KeyUtil.decodePublicKey(provider.getEncryptionKey().getPublicKey());
            }
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, EncryptKeyData.Fields.publicKey, Provider.Fields.code, code);
        }
    }

    @Override
    public PrivateKey getPrivateKeyByCode(String code) {
        try (Profiler p = new Profiler(getClass(), "getPrivateKeyByCode")) {
            var provider = getByCode(code);
            if (Objects.nonNull(provider) && Objects.nonNull(provider.getEncryptionKey()) && Objects.nonNull(provider.getEncryptionKey().getPrivateKey())) {
                return KeyUtil.decodePrivateKey(provider.getEncryptionKey().getPrivateKey());
            }
            throw new BusinessLogicException(AppErrorCode.NOT_FOUND, EncryptKeyData.Fields.privateKey, Provider.Fields.code, code);
        }
    }

    @Override
    public void invalidateCache(Long id, String code) {
        try (Profiler p = new Profiler(getClass(), "invalidateCache")) {
            providerCacheById.invalidate(id);
            codeToId.invalidate(code);
        }
    }

    private void reloadCache() {
        try (Profiler p = new Profiler(getClass(), "reloadCache")) {
            providerRepository.findAll().forEach(this::putToCache);
        }
    }

    private void putToCache(Provider provider) {
        try (Profiler p = new Profiler(getClass(), "putToCache")) {
            providerCacheById.put(provider.getId(), provider);
            codeToId.put(provider.getCode(), provider.getId());
        }
    }

    private void validateAndEnhanceCreateRequest(ProviderCreateDto createDto) {
        if (!StringUtils.hasText(createDto.getCode())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderCreateDto.Fields.code);
        }
        if (!StringUtils.hasText(createDto.getName())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, ProviderCreateDto.Fields.name);
        }
        if (Objects.isNull(createDto.getEncryptionKey()) && Objects.isNull(createDto.getEncryptKeySetting())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, String.format("%s hoặc %s", ProviderCreateDto.Fields.encryptionKey, ProviderCreateDto.Fields.encryptKeySetting));
        }

        if (Objects.nonNull(createDto.getEncryptKeySetting())) {
            // Create encrypt key data based on encryptKeySetting
            var keyData = KeyUtil.generateEncryptKeyData(createDto.getEncryptKeySetting());
            createDto.setEncryptionKey(keyData);
        }

        // validate encrypt key data
        validateEncryptionKey(createDto.getEncryptionKey());
    }

    private void applyUpdateRequest(Provider provider, ProviderUpdateDto updateDto) {
        if (StringUtils.hasText(updateDto.getName())) {
            provider.setName(updateDto.getName());
        }
        if (Objects.nonNull(updateDto.getStatus())) {
            provider.setStatus(updateDto.getStatus());
        }
        if (Objects.nonNull(updateDto.getEncryptKeySetting())) {
            var keyData = KeyUtil.generateEncryptKeyData(updateDto.getEncryptKeySetting());
            updateDto.setEncryptionKey(keyData);
        }
        if (Objects.nonNull(updateDto.getEncryptionKey())) {
            // validate encrypt key data
            validateEncryptionKey(updateDto.getEncryptionKey());
            provider.setEncryptionKey(updateDto.getEncryptionKey());
        }
        if (MapUtils.isNotEmpty(updateDto.getLogo())) {
            provider.setLogo(updateDto.getLogo());
        }
    }

    private void validateEncryptionKey(EncryptKeyData encryptionKey) {
        if (Objects.isNull(encryptionKey) || Objects.isNull(encryptionKey.getEncryptKeyType())) {
            throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, EncryptKeyData.Fields.encryptKeyType);
        }
        if (EncryptKeyTypeEnum.RSA.equals(encryptionKey.getEncryptKeyType())) {
            if (!StringUtils.hasText(encryptionKey.getPublicKey())) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, EncryptKeyData.Fields.publicKey);
            }
            if (!StringUtils.hasText(encryptionKey.getPrivateKey())) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, EncryptKeyData.Fields.privateKey);
            }
        } else if (EncryptKeyTypeEnum.AES.equals(encryptionKey.getEncryptKeyType())) {
            if (!StringUtils.hasText(encryptionKey.getSecretKey())) {
                throw new BusinessLogicException(AppErrorCode.PARAM_REQUIRED, EncryptKeyData.Fields.secretKey);
            }
        } else {
            throw new BusinessLogicException(AppErrorCode.INVALID_ENUM, encryptionKey.getEncryptKeyType(), EncryptKeyData.class.getSimpleName());
        }
    }
}
