package vn.vinclub.voucher.dto.provider.voucher;

import jakarta.persistence.criteria.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;
import vn.vinclub.voucher.model.ProviderVoucherListing;

import java.util.ArrayList;
import java.util.Objects;

public class ProviderVoucherListingSpecification {

    public static Specification<ProviderVoucherListing> filterByCriteria(ProviderVoucherListingFilterDto filter) {
        return (root, query, criteriaBuilder) -> {
            var predicates = new ArrayList<Predicate>();
            predicates.add(criteriaBuilder.isTrue(root.get(ProviderVoucherListing.Fields.active)));
            if (StringUtils.hasText(filter.getProviderMerchantCode())) {
                predicates.add(criteriaBuilder.equal(root.get(ProviderVoucherListing.Fields.providerMerchantCode), filter.getProviderMerchantCode()));
            }
            if (CollectionUtils.isNotEmpty(filter.getProviderVoucherListingIds())) {
                predicates.add(root.get(ProviderVoucherListing.Fields.providerVoucherListingId).in(filter.getProviderVoucherListingIds()));
            }
            if (StringUtils.hasText(filter.getVoucherName())) {
                predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get(ProviderVoucherListing.Fields.voucherName)), "%" + filter.getVoucherName().toLowerCase() + "%"));
            }
            if (StringUtils.hasText(filter.getProviderCode())) {
                predicates.add(criteriaBuilder.equal(root.get(ProviderVoucherListing.Fields.providerCode), filter.getProviderCode()));
            }
            if (Objects.nonNull(filter.getPromotionType())) {
                predicates.add(criteriaBuilder.equal(root.get(ProviderVoucherListing.Fields.promotionType), filter.getPromotionType()));
            }
            if (Objects.nonNull(filter.getStatus())) {
                predicates.add(criteriaBuilder.equal(root.get(ProviderVoucherListing.Fields.status), filter.getStatus()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
