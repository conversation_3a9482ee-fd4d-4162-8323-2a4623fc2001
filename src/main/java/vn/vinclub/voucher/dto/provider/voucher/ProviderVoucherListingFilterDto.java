package vn.vinclub.voucher.dto.provider.voucher;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;
import vn.vinclub.voucher.enums.PromotionTypeEnum;
import vn.vinclub.voucher.enums.VoucherListingStatusEnum;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProviderVoucherListingFilterDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private String providerCode;
    private String merchantCode;
    private List<String> providerVoucherListingIds;
    private String voucherName;
    private PromotionTypeEnum promotionType;

    @Builder.Default
    private VoucherListingStatusEnum status = VoucherListingStatusEnum.ACTIVE;

    // enhance
    @JsonIgnore
    private String providerMerchantCode;
}
